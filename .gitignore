# 모든 파일 무시
*

# 확장자가 있는 파일과 디렉터리는 추적
!*.*
!*/

# 확장자가 없지만 추적하고 싶은 파일은 예외로 명시
!README
!LICENSE
!NOTICE
!*akefile

.claude/
#bin/
obj/
CVS/
bak/
back/
bin_*/
domain/
Public/
script*/
library/

tags
*.o
*.a
*.so
cscope.*
*.log

ps_ef_file*
*.bak
*.lis
*.tar
*.gz
*.zip
*.7z
core.*
*.swp
*.table
#conf.tin
conf.tin.*
*.h.*
*.h_*
*.cpp.*
*.c.*
*.cpp_*
*.txt.*
*.txt_*
*akefile.*
*.conf.*
#*.conf
#*.cfg
*.cert
*.sql
*.key
vgcore.*
.cline*

!*.template

# CLion
.idea/
cmake-build-*/
CMakeCache.txt
CMakeFiles/
CMakeScripts/
cmake_install.cmake
compile_commands.json
CTestTestfile.cmake
_deps

# 단, CMake가 생성한 Makefile만 제외
**/cmake-build-*/Makefile
cmake-build-debug/Makefile
cmake-build-release/Makefile

# VS Code
.vscode/
*.code-workspace

# Build directories
build/
debug/
release/
out/

# CMake
CMakeUserPresets.json

# Database configuration files (contains sensitive information)
**/db_config.mk
db_config.mk
**/db_config.cmake
db_config.cmake
